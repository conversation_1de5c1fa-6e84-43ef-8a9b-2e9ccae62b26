import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/features/settings/controllers/settings_controller.dart';
import 'package:onechurch/features/settings/presentation/widgets/settings_tile.dart';
import '../widgets/settings_section.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsController = Get.find<SettingsController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Settings'), elevation: 0),
      body: Obx(() {
        if (settingsController.isLoading.value) {
          return const Center(child: CircleLoadingAnimation());
        }

        return ListView(
          children: [
            const SizedBox(height: 10),

            // Appearance Section
            SettingsSection(
              title: 'Appearance',
              children: [
                Obx(
                  () => SettingsTile(
                    title: 'Dark Mode',
                    subtitle: 'Toggle between light and dark theme',
                    leading: Icon(
                      settingsController.isDarkMode.value
                          ? Icons.dark_mode
                          : Icons.light_mode,
                      color:
                          settingsController.isDarkMode.value
                              ? Colors.white70
                              : Colors.amber,
                    ),
                    trailing: Switch(
                      value: settingsController.isDarkMode.value,
                      onChanged: (value) {
                        settingsController.updateTheme(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'Font Size',
                    subtitle: settingsController.selectedFontSize.value,
                    leading: const Icon(Icons.format_size),
                    onTap:
                        () => _showFontSizeDialog(context, settingsController),
                  ),
                ),
              ],
            ),

            // Notifications Section
            SettingsSection(
              title: 'Notifications',
              children: [
                Obx(
                  () => SettingsTile(
                    title: 'Push Notifications',
                    subtitle:
                        'Get notified about new events, sermons, and announcements',
                    leading: const Icon(Icons.notifications),
                    trailing: Switch(
                      value: settingsController.notificationsEnabled.value,
                      onChanged: (value) {
                        settingsController.updateNotificationSettings(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'Email Notifications',
                    subtitle: 'Receive notifications via email',
                    leading: const Icon(Icons.email),
                    trailing: Switch(
                      value: settingsController.emailNotifications.value,
                      onChanged: (value) {
                        settingsController.updateEmailNotifications(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'SMS Notifications',
                    subtitle: 'Receive notifications via SMS',
                    leading: const Icon(Icons.sms),
                    trailing: Switch(
                      value: settingsController.smsNotifications.value,
                      onChanged: (value) {
                        settingsController.updateSmsNotifications(value);
                      },
                    ),
                  ),
                ),
              ],
            ),

            // General Settings
            SettingsSection(
              title: 'General',
              children: [
                Obx(
                  () => SettingsTile(
                    title: 'Language',
                    subtitle: settingsController.selectedLanguage.value,
                    leading: const Icon(Icons.language),
                    onTap:
                        () => _showLanguageDialog(context, settingsController),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'Location Services',
                    subtitle: 'Enable location for nearby events',
                    leading: const Icon(Icons.location_on),
                    trailing: Switch(
                      value: settingsController.locationEnabled.value,
                      onChanged: (value) {
                        settingsController.updateLocationSettings(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'Autoplay Videos',
                    subtitle: 'Automatically play videos',
                    leading: const Icon(Icons.play_circle_outline),
                    trailing: Switch(
                      value: settingsController.autoplayVideos.value,
                      onChanged: (value) {
                        settingsController.updateAutoplaySettings(value);
                      },
                    ),
                  ),
                ),
              ],
            ),

            // Security Settings
            SettingsSection(
              title: 'Security',
              children: [
                Obx(
                  () => SettingsTile(
                    title: 'Biometric Authentication',
                    subtitle: 'Use fingerprint or face ID to unlock',
                    leading: const Icon(Icons.fingerprint),
                    trailing: Switch(
                      value: settingsController.biometricEnabled.value,
                      onChanged: (value) {
                        settingsController.updateBiometricSettings(value);
                      },
                    ),
                  ),
                ),
              ],
            ),

            // Data & Storage Settings
            SettingsSection(
              title: 'Data & Storage',
              children: [
                Obx(
                  () => SettingsTile(
                    title: 'Auto Sync',
                    subtitle: 'Automatically sync data',
                    leading: const Icon(Icons.sync),
                    trailing: Switch(
                      value: settingsController.autoSync.value,
                      onChanged: (value) {
                        settingsController.updateAutoSyncSettings(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'WiFi Only Sync',
                    subtitle: 'Only sync when connected to WiFi',
                    leading: const Icon(Icons.wifi),
                    trailing: Switch(
                      value: settingsController.wifiOnlySync.value,
                      onChanged: (value) {
                        settingsController.updateWifiOnlySyncSettings(value);
                      },
                    ),
                  ),
                ),
                Obx(
                  () => SettingsTile(
                    title: 'Clear Cache',
                    subtitle:
                        'Cache size: ${settingsController.cacheSize.value}',
                    leading: const Icon(Icons.storage),
                    onTap:
                        () =>
                            _showClearCacheDialog(context, settingsController),
                  ),
                ),
              ],
            ),

            // Account Settings
            SettingsSection(
              title: 'Account',
              children: [
                SettingsTile(
                  title: 'Edit Profile',
                  subtitle: 'Update your personal information',
                  leading: const Icon(Icons.person),
                  onTap: () {
                    context.go(Routes.PROFILE);
                  },
                ),
                SettingsTile(
                  title: 'Privacy Settings',
                  subtitle: 'Manage your privacy preferences',
                  leading: const Icon(Icons.security),
                  onTap: () {
                    // Navigate to privacy settings
                    Get.snackbar('Info', 'Privacy settings coming soon');
                  },
                ),
              ],
            ),

            // About Section
            SettingsSection(
              title: 'About',
              children: [
                SettingsTile(
                  title: 'About OneChurch',
                  subtitle: 'App version, terms of service, and more',
                  leading: const Icon(Icons.info_outline),
                  onTap: () => _showAboutDialog(context),
                ),
                SettingsTile(
                  title: 'Help & Support',
                  subtitle: 'Get help using the app',
                  leading: const Icon(Icons.help_outline),
                  onTap: () {
                    Get.snackbar('Info', 'Help & Support coming soon');
                  },
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Log Out Button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade800,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: () => _showLogOutDialog(context, settingsController),
                child: const Text('Log Out', style: TextStyle(fontSize: 16)),
              ),
            ),

            const SizedBox(height: 40),
          ],
        );
      }),
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    SettingsController controller,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  controller.languageOptions.map((language) {
                    return Obx(
                      () => ListTile(
                        title: Text(language),
                        leading: Radio<String>(
                          value: language,
                          groupValue: controller.selectedLanguage.value,
                          onChanged: (value) {
                            controller.updateLanguage(value!);
                            Navigator.pop(context);
                          },
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showFontSizeDialog(
    BuildContext context,
    SettingsController controller,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Font Size'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  controller.fontSizeOptions.map((size) {
                    return Obx(
                      () => ListTile(
                        title: Text(size),
                        leading: Radio<String>(
                          value: size,
                          groupValue: controller.selectedFontSize.value,
                          onChanged: (value) {
                            controller.updateFontSize(value!);
                            Navigator.pop(context);
                          },
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showClearCacheDialog(
    BuildContext context,
    SettingsController controller,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Clear Cache'),
          content: Text(
            'This will clear ${controller.cacheSize.value} of cached data. Continue?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                controller.clearCache();
                Navigator.pop(context);
              },
              child: const Text('Clear', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('About OneChurch'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('OneChurch App v1.0.0'),
              SizedBox(height: 10),
              Text(
                'A complete church community app that helps connect members, manage events, and share sermons.',
              ),
              SizedBox(height: 20),
              Text('© 2023 OneChurch. All rights reserved.'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showLogOutDialog(BuildContext context, SettingsController controller) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Log Out'),
          content: const Text('Are you sure you want to log out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Implement actual logout logic here
                Get.snackbar('Success', 'Logged out successfully');
                // You would typically call authController.logout() here
              },
              child: const Text('Log Out', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
