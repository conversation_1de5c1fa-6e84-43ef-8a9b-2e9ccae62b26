import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../controllers/auth_controller.dart';
import '../../../../core/widgets/custom_phone_input.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final AuthController _authController = Get.find<AuthController>();
  final _formKey = GlobalKey<FormState>();
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  void _onPhoneNumberChanged(PhoneNumber number) {
    _authController.countryCodeController.text = number.dialCode ?? '';
    _authController.phoneController.text = number.parseNumber();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Forgot Password',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(IconlyLight.arrowLeft),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Forgot Password',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Enter your phone number or email to reset your password',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 32),

                // Email input
                CustomTextField(
                  label: 'Email Address',
                  controller: _authController.emailController,
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: Icon(IconlyLight.message),
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                    } else if (_authController.phoneController.text.isEmpty) {
                      return 'Please provide either email or phone number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Phone input
                CustomPhoneInput(
                  label: 'Phone Number',
                  controller: _authController.phoneController,
                  onPhoneNumberChanged: _onPhoneNumberChanged,
                  errorText: null,
                ),

                const SizedBox(height: 40),

                // Error message
                Obx(
                  () =>
                      _errorMessage.isNotEmpty
                          ? Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 16,
                            ),
                            margin: const EdgeInsets.only(bottom: 24),
                            decoration: BoxDecoration(
                              color: colorScheme.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: colorScheme.error.withValues(alpha: 0.5),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: colorScheme.error,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _errorMessage.value,
                                    style: TextStyle(color: colorScheme.error),
                                  ),
                                ),
                              ],
                            ),
                          )
                          : const SizedBox.shrink(),
                ),

                // Submit button
                SizedBox(
                  width: double.infinity,
                  child: Obx(
                    () => ElevatedButton(
                      onPressed:
                          _isLoading.value
                              ? null
                              : () {
                                _initiatePasswordReset();
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 2,
                      ),
                      child:
                          _isLoading.value
                              ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircleLoadingAnimation(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    colorScheme.onPrimary,
                                  ),
                                ),
                              )
                              : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(IconlyLight.send, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Send Reset Link',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Clear controllers when this screen is disposed
    _authController.emailController.clear();
    _authController.phoneController.clear();
    _authController.countryCodeController.clear();
    super.dispose();
  }

  // Send OTP to initiate password reset
  Future<void> _initiatePasswordReset() async {
    if (_formKey.currentState!.validate()) {
      FocusScope.of(context).unfocus();
      bool success = await _authController.initiatePasswordReset(context);

      if (!success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_authController.errorMessage.value),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
