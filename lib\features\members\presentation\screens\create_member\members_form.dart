import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/custom_dropdown.dart';
import 'package:onechurch/core/widgets/custom_phone_input.dart';
import 'package:onechurch/data/models/member_model.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/members/models/member_category_model.dart'
    as features_member_category;
import 'package:onechurch/data/models/member_category_model.dart'
    as data_member_category;
import 'package:onechurch/features/members/presentation/select-org/member_category_selection_dialog.dart';

class MemberFormDialog extends StatefulWidget {
  final MemberModel? member;
  final Function(MemberModel) onSave;

  const MemberFormDialog({super.key, this.member, required this.onSave});

  @override
  State<MemberFormDialog> createState() => _MemberFormDialogState();
}

class _MemberFormDialogState extends State<MemberFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final authController = Get.find<AuthController>();
  final logger = Get.find<Logger>();

  // Form flow state - using GetX reactive variables
  late RxBool showBasicInfoOnly;
  late RxBool isChild;
  late RxnInt age;
  late RxBool canSave;

  // Form controllers
  late TextEditingController firstNameController;
  late TextEditingController secondNameController;
  late TextEditingController phoneController;
  late TextEditingController secondaryPhoneController;
  late TextEditingController emailController;
  late TextEditingController idNumberController;
  late TextEditingController addressController;
  late TextEditingController nationalityController;
  late TextEditingController occupationController;
  late TextEditingController tribeController;
  late TextEditingController householdSizeController;
  late TextEditingController incomeBracketController;
  late TextEditingController otherAssetController;
  late TextEditingController accountNumber;

  // Reactive form data
  late Rxn<DateTime> joinDate;
  late Rxn<DateTime> dob;
  late Rxn<DateTime> baptismDate;
  late RxnString gender;
  late RxnString maritalStatus;
  late RxnString educationLevel;
  late RxnString disability;
  late RxnString employmentStatus;
  late RxnString housingType;
  late RxList<String> assetOwnership;

  // Enum reactive variables for dropdowns
  late Rxn<Gender> selectedGender;
  late Rxn<MaritalStatus> selectedMaritalStatus;
  late Rxn<Disability> selectedDisability;
  late Rxn<EmploymentStatus> selectedEmploymentStatus;
  late Rxn<HousingType> selectedHousingType;
  late Rxn<IncomeBracket> selectedIncomeBracket;

  // Selected category
  late Rxn<features_member_category.MemberCategory> selectedCategory;
  late RxnString locationId;
  final educationLevelOptions = [
    'PRIMARY',
    'SECONDARY',
    'DIPLOMA',
    'DEGREE',
    'MASTERS',
    'PHD',
  ];

  // Calculate age from date of birth
  int? _calculateAge(DateTime? birthDate) {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  // Update form flow based on age and clear inappropriate fields
  void _updateFormFlow() {
    if (dob.value != null) {
      age.value = _calculateAge(dob.value);
      final wasChild = isChild.value;
      isChild.value = age.value != null && age.value! < 18;
      showBasicInfoOnly.value = false;

      // Clear fields when age category changes
      if (wasChild != isChild.value) {
        _clearAgeInappropriateFields();
      }

      // Generate account number for children
      if (isChild.value &&
          accountNumber.text.isEmpty &&
          phoneController.text.isNotEmpty) {
        final random = Random();
        accountNumber.text =
            '${phoneController.text}#${random.nextInt(100) + 1}';
      }

      _validateForm();
    }
  }

  // Clear fields that are inappropriate for the current age category
  void _clearAgeInappropriateFields() {
    if (isChild.value) {
      // Clear adult-only fields for children
      idNumberController.clear();
      maritalStatus.value = 'SINGLE';
      occupationController.clear();
      employmentStatus.value = null;
      incomeBracketController.clear();
    }
  }

  // Validate form and update save button state
  void _validateForm() {
    final hasRequiredFields =
        firstNameController.text.isNotEmpty &&
        secondNameController.text.isNotEmpty &&
        phoneController.text.isNotEmpty &&
        accountNumber.text.isNotEmpty &&
        dob.value != null &&
        joinDate.value != null &&
        gender.value != null &&
        selectedCategory.value != null;

    final hasAdultRequiredFields =
        isChild.value || (idNumberController.text.isNotEmpty);

    canSave.value = hasRequiredFields && hasAdultRequiredFields;
  }

  late RxBool showSecondNumber;

  @override
  void initState() {
    super.initState();

    // Initialize reactive variables
    showBasicInfoOnly = true.obs;
    isChild = false.obs;
    age = RxnInt();
    canSave = false.obs;
    joinDate = Rxn<DateTime>();
    dob = Rxn<DateTime>();
    baptismDate = Rxn<DateTime>();
    gender = RxnString();
    maritalStatus = RxnString();
    educationLevel = RxnString();
    disability = RxnString();
    employmentStatus = RxnString();
    housingType = RxnString();
    assetOwnership = <String>[].obs;
    selectedCategory = Rxn<features_member_category.MemberCategory>();
    locationId = RxnString();
    showSecondNumber = false.obs;

    // Initialize enum reactive variables
    selectedGender = Rxn<Gender>();
    selectedMaritalStatus = Rxn<MaritalStatus>();
    selectedDisability = Rxn<Disability>();
    selectedEmploymentStatus = Rxn<EmploymentStatus>();
    selectedHousingType = Rxn<HousingType>();
    selectedIncomeBracket = Rxn<IncomeBracket>();
    selectedCategory.value =
        widget.member?.memberCategory != null
            ? features_member_category.MemberCategory.fromJson(
              widget.member!.memberCategory!.toJson(),
            )
            : null;

    // Initialize controllers with member data if editing
    firstNameController = TextEditingController(
      text: widget.member?.firstName ?? '',
    );
    secondNameController = TextEditingController(
      text: widget.member?.secondName ?? '',
    );
    phoneController = TextEditingController(
      text: widget.member?.phoneNumber ?? '',
    );
    secondaryPhoneController = TextEditingController(
      text: widget.member?.secondaryNumber ?? '',
    );
    emailController = TextEditingController(text: widget.member?.email ?? '');
    idNumberController = TextEditingController(
      text: widget.member?.idNumber ?? '',
    );
    addressController = TextEditingController(
      text: widget.member?.address ?? '',
    );
    nationalityController = TextEditingController(
      text: widget.member?.nationality ?? 'Kenyan',
    );
    occupationController = TextEditingController(
      text: widget.member?.occupation ?? '',
    );
    tribeController = TextEditingController(text: widget.member?.tribe ?? '');
    householdSizeController = TextEditingController(
      text: widget.member?.householdSize?.toString() ?? '',
    );
    incomeBracketController = TextEditingController(
      text: widget.member?.incomeBracket ?? '',
    );
    otherAssetController = TextEditingController();

    joinDate.value = widget.member?.joinDate ?? DateTime.now();
    accountNumber = TextEditingController(
      text: widget.member?.accountNumber ?? '',
    );
    dob.value = widget.member?.dob;
    baptismDate.value =
        widget.member?.baptismDate != null
            ? (widget.member!.baptismDate is DateTime
                ? widget.member!.baptismDate as DateTime
                : null)
            : null;
    gender.value = widget.member?.gender ?? 'MALE';
    maritalStatus.value = widget.member?.maritalStatus ?? 'SINGLE';
    educationLevel.value = widget.member?.educationLevel ?? 'PRIMARY';
    disability.value = widget.member?.disability ?? 'NONE';
    employmentStatus.value = widget.member?.employmentStatus ?? 'EMPLOYED';
    housingType.value = widget.member?.housingType ?? 'PERMANENT';

    // Initialize enum values from member data
    selectedGender.value =
        Gender.values.firstWhereOrNull(
          (e) => e.displayName == (widget.member?.gender ?? 'MALE'),
        ) ??
        Gender.male;
    selectedMaritalStatus.value =
        MaritalStatus.values.firstWhereOrNull(
          (e) => e.displayName == (widget.member?.maritalStatus ?? 'SINGLE'),
        ) ??
        MaritalStatus.single;
    selectedDisability.value =
        Disability.values.firstWhereOrNull(
          (e) => e.displayName == (widget.member?.disability ?? 'NONE'),
        ) ??
        Disability.none;
    selectedEmploymentStatus.value =
        EmploymentStatus.values.firstWhereOrNull(
          (e) =>
              e.displayName == (widget.member?.employmentStatus ?? 'EMPLOYED'),
        ) ??
        EmploymentStatus.employed;
    selectedHousingType.value =
        HousingType.values.firstWhereOrNull(
          (e) => e.displayName == (widget.member?.housingType ?? 'PERMANENT'),
        ) ??
        HousingType.permanent;
    selectedIncomeBracket.value = IncomeBracket.values.firstWhereOrNull(
      (e) => e.displayName == widget.member?.incomeBracket,
    );

    // If editing existing member, check if they're a child and update form flow
    if (widget.member != null) {
      isChild.value = widget.member?.isChild ?? false;
      if (dob.value != null) {
        age.value = _calculateAge(dob.value);
        showBasicInfoOnly.value = false;
      }
    }

    // Initialize asset ownership
    if (widget.member?.assetOwnership != null) {
      if (widget.member!.assetOwnership is List) {
        // If it's already a list, convert each element to string
        assetOwnership.value =
            (widget.member!.assetOwnership as List)
                .map((e) => e.toString())
                .toList();
      } else if (widget.member!.assetOwnership is String) {
        try {
          // Try to parse as JSON if it's a string
          final List<dynamic> parsed = jsonDecode(
            widget.member!.assetOwnership.toString(),
          );
          assetOwnership.value = parsed.map((e) => e.toString()).toList();
        } catch (e) {
          // If it's a single string but not JSON, add it as a single item
          if (widget.member!.assetOwnership.toString().isNotEmpty) {
            assetOwnership.value = [widget.member!.assetOwnership.toString()];
          } else {
            assetOwnership.value = ['NONE'];
          }
        }
      }
    } else {
      assetOwnership.value = ['NONE'];
    }

    // Make sure we have valid asset ownership values
    assetOwnership.value =
        assetOwnership
            .where(
              (asset) =>
                  AssetOwnership.values.any((e) => e.displayName == asset),
            )
            .toList();

    // Set member category if available
    if (widget.member?.memberCategoryId != null) {
      selectedCategory.value = features_member_category.MemberCategory(
        id: widget.member!.memberCategoryId.toString(),
        title:
            widget.member?.memberCategory != null
                ? (widget.member!.memberCategory is Map
                    ? widget.member!.memberCategory?.title
                    : 'Selected Category')
                : 'Selected Category',
      );
    }

    // Set location ID if available
    locationId.value = widget.member?.locationId?.toString();

    // Add listeners to text controllers for validation
    _addTextControllerListeners();

    // Initial validation
    _validateForm();
  }

  // Add listeners to text controllers for real-time validation
  void _addTextControllerListeners() {
    firstNameController.addListener(_validateForm);
    secondNameController.addListener(_validateForm);
    phoneController.addListener(_validateForm);
    accountNumber.addListener(_validateForm);
    idNumberController.addListener(_validateForm);
  }

  @override
  void dispose() {
    firstNameController.dispose();
    secondNameController.dispose();
    phoneController.dispose();
    secondaryPhoneController.dispose();
    emailController.dispose();
    idNumberController.dispose();
    addressController.dispose();
    nationalityController.dispose();
    occupationController.dispose();
    tribeController.dispose();
    householdSizeController.dispose();
    incomeBracketController.dispose();
    otherAssetController.dispose();
    accountNumber.dispose();
    super.dispose();
  }

  Future<void> _selectDate(
    BuildContext context,
    bool isJoinDate, {
    bool isBaptismDate = false,
  }) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isJoinDate
              ? (joinDate.value ?? DateTime.now())
              : (isBaptismDate
                  ? (baptismDate.value ?? DateTime.now())
                  : (dob.value ?? DateTime(2000))),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      if (isJoinDate) {
        joinDate.value = picked;
      } else if (isBaptismDate) {
        baptismDate.value = picked;
      } else {
        dob.value = picked;
        // Update form flow when DOB is selected
        _updateFormFlow();
      }
      _validateForm();
    }
  }

  Future<void> _selectMemberCategory() async {
    final features_member_category.MemberCategory? selectedCat =
        await MemberCategorySelectionDialog.show(context);
    if (selectedCat != null) {
      selectedCategory.value = selectedCat;
      _validateForm();
    }
  }

  // Asset ownership is now handled directly in the dropdown

  /// Convert features MemberCategory to data MemberCategory
  data_member_category.MemberCategory? _convertMemberCategory(
    features_member_category.MemberCategory? featureCategory,
  ) {
    if (featureCategory == null) return null;

    return data_member_category.MemberCategory(
      id: featureCategory.id,
      title: featureCategory.title,
      description: featureCategory.description,
      code: featureCategory.code,
      organisationId: featureCategory.organisationId,
      isGeneral: featureCategory.isGeneral,
      isActive:
          true, // Default to active since features model doesn't have this
      createdAt:
          featureCategory.createdAt != null
              ? DateTime.tryParse(featureCategory.createdAt!)
              : null,
      updatedAt:
          featureCategory.updatedAt != null
              ? DateTime.tryParse(featureCategory.updatedAt!)
              : null,
      deletedAt:
          featureCategory.deletedAt != null
              ? DateTime.tryParse(featureCategory.deletedAt!)
              : null,
    );
  }

  void _saveMember() {
    if (_formKey.currentState!.validate()) {
      int? householdSize;
      try {
        householdSize = int.parse(householdSizeController.text);
      } catch (e) {
        householdSize = 1;
      }

      // Create a MemberModel for display in the grid
      final member = MemberModel(
        firstName: firstNameController.text,
        secondName: secondNameController.text,

        phoneNumber: phoneController.text,
        secondaryNumber:
            secondaryPhoneController.text.isEmpty
                ? null
                : secondaryPhoneController.text,
        email: emailController.text,
        idNumber: isChild.value ? null : idNumberController.text,
        accountNumber: accountNumber.text,
        address: addressController.text,
        joinDate: joinDate.value,
        dob: dob.value,
        baptismDate: baptismDate.value,
        gender: gender.value,
        maritalStatus: isChild.value ? 'SINGLE' : maritalStatus.value,
        educationLevel: educationLevel.value,
        nationality: nationalityController.text,
        occupation: isChild.value ? null : occupationController.text,
        tribe: tribeController.text,
        disability: disability.value,
        employmentStatus: isChild.value ? null : employmentStatus.value,
        incomeBracket: isChild.value ? null : incomeBracketController.text,
        householdSize: householdSize,
        housingType: housingType.value,
        // Ensure asset ownership is saved as a proper array
        assetOwnership:
            assetOwnership.isEmpty
                ? []
                : assetOwnership.contains('OTHERS')
                ? [otherAssetController.text, ...assetOwnership]
                : assetOwnership,
        organisationId: authController.currentOrg.value?.id.toString(),
        memberCategoryId: selectedCategory.value?.id,
        memberCategory: _convertMemberCategory(selectedCategory.value),
        locationId: locationId.value,
        balance: 0,
        profileUrl: '',
        isChild: isChild.value,
      );

      widget.onSave(member);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Personal Information
            Text(
              'Personal Information',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Gap(16.h),

            // First Name
            CustomTextFormField(
              controller: firstNameController,
              labelText: 'First Name *',
              prefixIcon: Icon(
                Icons.person_outline,
                color: colorScheme.primary,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter first name';
                }
                return null;
              },
            ),
            Gap(16.h),

            // Second Name
            CustomTextFormField(
              controller: secondNameController,
              labelText: 'Second Name *',
              prefixIcon: Icon(
                Icons.person_outline,
                color: colorScheme.primary,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter second name';
                }
                return null;
              },
            ),
            Gap(16.h),

            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Date of Birth *',
                  prefixIcon: Icon(
                    Icons.calendar_today,
                    color: colorScheme.primary,
                  ),
                  filled: true,
                  fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.outline),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.outline.withOpacity(0.5),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.error),
                  ),
                ),
                child: Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        dob.value != null
                            ? DateFormat('dd MMM yyyy').format(dob.value!)
                            : 'Select Date of Birth',
                        style: TextStyle(color: colorScheme.onSurface),
                      ),
                      Icon(Icons.arrow_drop_down, color: colorScheme.primary),
                    ],
                  ),
                ),
              ),
            ),
            Gap(16.h),

            // Show age info if DOB is selected
            Obx(() {
              if (age.value != null) {
                return Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(12.r),
                      decoration: BoxDecoration(
                        color:
                            isChild.value
                                ? Colors.blue.withOpacity(0.1)
                                : Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isChild.value ? Colors.blue : Colors.green,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            isChild.value ? Icons.child_care : Icons.person,
                            color: isChild.value ? Colors.blue : Colors.green,
                          ),
                          Gap(8.w),
                          Text(
                            'Age: ${age.value} years ${isChild.value ? '(Minor)' : '(Adult)'}',
                            style: TextStyle(
                              color: isChild.value ? Colors.blue : Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gap(16.h),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Show remaining fields only after DOB is selected
            Obx(() {
              if (!showBasicInfoOnly.value) {
                return Column(
                  children: [
                    // Phone Number
                    CustomPhoneInput(
                      label: 'Phone Number *',
                      controller: phoneController,
                      initialPhoneNumber:
                          widget.member?.phoneNumber != null
                              ? PhoneNumber(
                                isoCode: 'KE',
                                phoneNumber: widget.member?.phoneNumber,
                              )
                              : null,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter phone number';
                        }
                        return null;
                      },
                      onPhoneNumberChanged: (PhoneNumber number) {
                        phoneController.text = number.phoneNumber ?? '';
                        if (isChild.value && accountNumber.text.isEmpty) {
                          final random = Random();
                          accountNumber.text =
                              '${number.phoneNumber}#${random.nextInt(100) + 1}';
                        }
                        accountNumber.text = number.parseNumber();
                        _validateForm();
                      },
                    ),
                    Gap(4.h),

                    Obx(
                      () => Column(
                        children: [
                          CheckboxListTile(
                            title: Text('Has secondary phone number?'),
                            value: showSecondNumber.value,
                            onChanged: (value) {
                              showSecondNumber.value = value ?? false;
                            },
                          ),
                          showSecondNumber.value
                              ? CustomPhoneInput(
                                label: 'Secondary Phone Number (Optional)',
                                controller: secondaryPhoneController,
                                validator: null,
                                onPhoneNumberChanged: (PhoneNumber number) {
                                  secondaryPhoneController.text =
                                      number.phoneNumber ?? '';
                                },
                              )
                              : SizedBox(),
                        ],
                      ),
                    ),

                    Gap(16.h),

                    // Account Number
                    CustomTextFormField(
                      controller: accountNumber,
                      labelText: 'account number *',
                      prefixIcon: Icon(
                        Icons.person_outline,
                        color: colorScheme.primary,
                      ),

                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please AccountNumber';
                        }
                        return null;
                      },
                    ),
                    Gap(16.h),

                    // Email
                    CustomTextFormField(
                      controller: emailController,
                      labelText: 'Email',
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        color: colorScheme.primary,
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    Gap(16.h),

                    // ID Number (only for adults)
                    if (!isChild.value) ...[
                      CustomTextFormField(
                        controller: idNumberController,
                        labelText: 'ID Number *',
                        prefixIcon: Icon(
                          Icons.badge_outlined,
                          color: colorScheme.primary,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter ID number';
                          }
                          return null;
                        },
                      ),
                      Gap(16.h),
                    ],

                    // Member Category Selection
                    InkWell(
                      onTap: _selectMemberCategory,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Member Category *',
                          prefixIcon: Icon(
                            Icons.category_outlined,
                            color: colorScheme.primary,
                          ),
                          filled: true,
                          fillColor: colorScheme.surfaceVariant.withOpacity(
                            0.3,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.outline),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.5),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.error),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Obx(
                              () => Text(
                                selectedCategory.value != null
                                    ? selectedCategory.value!.title ??
                                        'Selected Category'
                                    : 'Select Member Category',
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: colorScheme.primary,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Gap(16.h),

                    // Gender
                    CustomDropdown<Gender>(
                      labelText: 'Gender *',
                      value: selectedGender.value,
                      initialValue: Gender.male,
                      prefixIcon: Icon(
                        Icons.person_outline,
                        color: colorScheme.primary,
                      ),
                      items:
                          Gender.values.map((Gender value) {
                            return DropdownMenuItem<Gender>(
                              value: value,
                              child: Text(value.displayName),
                            );
                          }).toList(),
                      validator: (value) {
                        if (value == null) {
                          return 'Please select gender';
                        }
                        return null;
                      },
                      onChanged: (newValue) {
                        if (newValue != null) {
                          selectedGender.value = newValue;
                          gender.value = newValue.displayName;
                          _validateForm();
                        }
                      },
                    ),
                    Gap(16.h),

                    // Join Date
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Join Date *',
                          prefixIcon: Icon(
                            Icons.calendar_today,
                            color: colorScheme.primary,
                          ),
                          filled: true,
                          fillColor: colorScheme.surfaceVariant.withOpacity(
                            0.3,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.outline),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.5),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.error),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Obx(
                              () => Text(
                                joinDate.value != null
                                    ? DateFormat(
                                      'dd MMM yyyy',
                                    ).format(joinDate.value!)
                                    : 'Select Join Date',
                                style: TextStyle(color: colorScheme.onSurface),
                              ),
                            ),
                            Icon(
                              Icons.arrow_drop_down,
                              color: colorScheme.primary,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Gap(16.h),

                    // Baptism Date
                    InkWell(
                      onTap:
                          () =>
                              _selectDate(context, false, isBaptismDate: true),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Baptism Date',
                          prefixIcon: Icon(
                            Icons.calendar_today,
                            color: colorScheme.primary,
                          ),
                          filled: true,
                          fillColor: colorScheme.surfaceVariant.withOpacity(
                            0.3,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.outline),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.5),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: colorScheme.error),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Obx(
                              () => Text(
                                baptismDate.value != null
                                    ? DateFormat(
                                      'dd MMM yyyy',
                                    ).format(baptismDate.value!)
                                    : 'Select Baptism Date',
                                style: TextStyle(color: colorScheme.onSurface),
                              ),
                            ),
                            Icon(
                              Icons.arrow_drop_down,
                              color: colorScheme.primary,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Gap(16.h),

                    // Additional Information
                    Text(
                      'Additional Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Gap(16.h),

                    // Address
                    CustomTextFormField(
                      controller: addressController,

                      labelText: 'Address',

                      maxLines: 2,
                    ),
                    Gap(16.h),

                    // Marital Status (only for adults)
                    if (!isChild.value) ...[
                      CustomDropdown<MaritalStatus>(
                        labelText: 'Marital Status',
                        value: selectedMaritalStatus.value,
                        initialValue: MaritalStatus.single,
                        prefixIcon: Icon(
                          Icons.family_restroom,
                          color: colorScheme.primary,
                        ),
                        items:
                            MaritalStatus.values.map((MaritalStatus value) {
                              return DropdownMenuItem<MaritalStatus>(
                                value: value,
                                child: Text(value.displayName),
                              );
                            }).toList(),
                        onChanged: (newValue) {
                          if (newValue != null) {
                            selectedMaritalStatus.value = newValue;
                            maritalStatus.value = newValue.displayName;
                            _validateForm();
                          }
                        },
                      ),
                      Gap(16.h),

                      // Occupation
                      CustomTextFormField(
                        controller: occupationController,
                        labelText: 'Occupation',
                      ),
                      Gap(16.h),
                    ],

                    // Education Level
                    CustomDropdown<String>(
                      labelText: 'Education Level',
                      value: educationLevel.value,
                      initialValue: 'PRIMARY',
                      prefixIcon: Icon(
                        Icons.school,
                        color: colorScheme.primary,
                      ),
                      items:
                          educationLevelOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                      onChanged: (newValue) {
                        if (newValue != null) {
                          educationLevel.value = newValue;
                          _validateForm();
                        }
                      },
                    ),
                    Gap(16.h),

                    // Tribe
                    CustomTextFormField(
                      controller: tribeController,

                      labelText: 'Tribe',
                    ),
                    Gap(16.h),

                    // Nationality
                    CustomTextFormField(
                      controller: nationalityController,

                      labelText: 'Nationality',
                    ),
                    Gap(16.h),

                    // Disability
                    CustomDropdown<Disability>(
                      labelText: 'Disability',
                      value: selectedDisability.value,
                      initialValue: Disability.none,
                      prefixIcon: Icon(
                        Icons.accessible,
                        color: colorScheme.primary,
                      ),
                      items:
                          Disability.values.map((Disability value) {
                            return DropdownMenuItem<Disability>(
                              value: value,
                              child: Text(value.displayName),
                            );
                          }).toList(),
                      onChanged: (newValue) {
                        if (newValue != null) {
                          selectedDisability.value = newValue;
                          disability.value = newValue.displayName;
                          _validateForm();
                        }
                      },
                    ),
                    Gap(16.h),

                    // Socioeconomic Information (only for adults)
                    Obx(() {
                      if (!isChild.value) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Socioeconomic Information',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            Gap(16.h),

                            // Employment Status
                            CustomDropdown<EmploymentStatus>(
                              labelText: 'Employment Status',
                              value: selectedEmploymentStatus.value,
                              initialValue: EmploymentStatus.unemployed,
                              prefixIcon: Icon(
                                Icons.work,
                                color: colorScheme.primary,
                              ),
                              items:
                                  EmploymentStatus.values.map((
                                    EmploymentStatus value,
                                  ) {
                                    return DropdownMenuItem<EmploymentStatus>(
                                      value: value,
                                      child: Text(value.displayName),
                                    );
                                  }).toList(),
                              onChanged: (newValue) {
                                if (newValue != null) {
                                  selectedEmploymentStatus.value = newValue;
                                  employmentStatus.value = newValue.displayName;
                                  _validateForm();
                                }
                              },
                            ),
                            Gap(16.h),

                            // Income Bracket
                            CustomDropdown<IncomeBracket>(
                              labelText: 'Income Bracket',
                              value: selectedIncomeBracket.value,
                              initialValue: IncomeBracket.lessThan10k,
                              prefixIcon: Icon(
                                Icons.attach_money,
                                color: colorScheme.primary,
                              ),
                              items:
                                  IncomeBracket.values.map((
                                    IncomeBracket value,
                                  ) {
                                    return DropdownMenuItem<IncomeBracket>(
                                      value: value,
                                      child: Text(value.displayName),
                                    );
                                  }).toList(),
                              onChanged: (newValue) {
                                if (newValue != null) {
                                  selectedIncomeBracket.value = newValue;
                                  incomeBracketController.text =
                                      newValue.displayName;
                                }
                              },
                            ),
                            Gap(16.h),
                          ],
                        );
                      } else {
                        return SizedBox();
                      }
                    }),

                    // Household Size
                    CustomTextFormField(
                      controller: householdSizeController,
                      labelText: 'Household Size',
                      prefixIcon: Icon(
                        Icons.people_outline,
                        color: colorScheme.primary,
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    Gap(16.h),

                    // Housing Type
                    CustomDropdown<HousingType>(
                      labelText: 'Housing Type',
                      value: selectedHousingType.value,
                      initialValue: HousingType.permanent,
                      prefixIcon: Icon(
                        Icons.home_outlined,
                        color: colorScheme.primary,
                      ),
                      items:
                          HousingType.values.map((HousingType value) {
                            return DropdownMenuItem<HousingType>(
                              value: value,
                              child: Text(value.displayName),
                            );
                          }).toList(),
                      onChanged: (newValue) {
                        if (newValue != null) {
                          selectedHousingType.value = newValue;
                          housingType.value = newValue.displayName;
                          _validateForm();
                        }
                      },
                    ),
                    Gap(16.h),

                    // Asset Ownership - Multi-select
                    InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Asset Ownership',
                        prefixIcon: Icon(
                          Icons.account_balance_outlined,
                          color: colorScheme.primary,
                        ),
                        filled: true,
                        fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: colorScheme.outline),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.outline.withOpacity(0.5),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: colorScheme.error),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 8.0,
                            runSpacing: 8.0,
                            children:
                                AssetOwnership.values.map((asset) {
                                  final isSelected = assetOwnership.contains(
                                    asset.displayName,
                                  );
                                  return FilterChip(
                                    label: Text(asset.displayName),
                                    selected: isSelected,
                                    onSelected: (selected) {
                                      if (selected) {
                                        // Add to the list if not already there
                                        if (!assetOwnership.contains(
                                          asset.displayName,
                                        )) {
                                          assetOwnership.add(asset.displayName);
                                        }
                                      } else {
                                        // Remove from the list
                                        assetOwnership.removeWhere(
                                          (item) => item == asset.displayName,
                                        );
                                      }
                                      _validateForm();
                                    },
                                    selectedColor: colorScheme.primary
                                        .withOpacity(0.2),
                                    checkmarkColor: colorScheme.primary,
                                    backgroundColor: colorScheme.surface,
                                  );
                                }).toList(),
                          ),
                          if (assetOwnership.contains("OTHER"))
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CustomTextFormField(
                                controller: otherAssetController,
                                labelText: 'Other Asset',
                                prefixIcon: Icon(
                                  Icons.account_balance_outlined,
                                  color: colorScheme.primary,
                                ),
                              ),
                            ),
                          if (assetOwnership.isEmpty)
                            Padding(
                              padding: EdgeInsets.only(top: 8.0),
                              child: Text(
                                'Select at least one asset',
                                style: TextStyle(
                                  color: colorScheme.error,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Gap(24.h),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Save button (always show)
            Obx(
              () => Container(
                margin: EdgeInsets.only(top: 16.h),
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: canSave.value ? _saveMember : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        canSave.value
                            ? colorScheme.primary
                            : colorScheme.outline.withOpacity(0.3),
                    foregroundColor:
                        canSave.value
                            ? colorScheme.onPrimary
                            : colorScheme.onSurface.withOpacity(0.5),
                    elevation: canSave.value ? 2 : 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'SAVE MEMBER',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MemberFormBottomSheet extends StatelessWidget {
  final MemberModel? member;
  final Function(MemberModel) onSave;

  const MemberFormBottomSheet({super.key, this.member, required this.onSave});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      expand: false,
      builder: (context, scrollController) {
        return Column(
          children: [
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Row(
                children: [
                  Text(
                    member != null ? 'Edit Member' : 'Add New Member',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                child: MemberFormDialog(member: member, onSave: onSave),
              ),
            ),
          ],
        );
      },
    );
  }
}
