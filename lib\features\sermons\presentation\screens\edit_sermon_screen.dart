import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_autocomplete.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/features/media_upload/presentation/widgets/media_upload_widget.dart';
import '../../../../core/app/utils/size_config.dart';
import '../../controllers/sermon_controller.dart';
import '../../../../data/models/sermon_model.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class EditSermonScreen extends StatefulWidget {
  final String sermonId;
  final SermonModel? initialSermon;

  const EditSermonScreen({
    super.key,
    required this.sermonId,
    this.initialSermon,
  });

  @override
  State<EditSermonScreen> createState() => _EditSermonScreenState();
}

class _EditSermonScreenState extends State<EditSermonScreen> {
  final controller = Get.find<SermonController>();
  final _formKey = GlobalKey<FormState>();
  String _description = '';
  bool isLoading = true;
  SermonModel? sermon;

  @override
  void initState() {
    super.initState();
    _initializeSermon();
  }

  Future<void> _initializeSermon() async {
    if (widget.initialSermon != null) {
      // Load the initial sermon immediately
      sermon = widget.initialSermon;
      _loadSermonForEdit(widget.initialSermon!);
      setState(() {
        isLoading = false;
      });

      // Optionally fetch fresh data in the background
      _refreshSermonForEdit();
    } else {
      // If no initial sermon, fetch it
      await _loadSermon();
    }
  }

  void _loadSermonForEdit(SermonModel sermonModel) {
    controller.loadSermonForEdit(sermonModel);
    // The description will be handled by the MarkupEditorWidget through editingDescription
    _description = sermonModel.description ?? '';
  }

  Future<void> _loadSermon() async {
    setState(() {
      isLoading = true;
    });

    final loadedSermon = await controller.getSermonById(widget.sermonId);

    if (loadedSermon != null) {
      sermon = loadedSermon;
      _loadSermonForEdit(loadedSermon);
    }

    setState(() {
      isLoading = false;
    });
  }

  // Background refresh for edit screen
  Future<void> _refreshSermonForEdit() async {
    try {
      final freshSermon = await controller.getSermonById(widget.sermonId);
      if (freshSermon != null && mounted) {
        sermon = freshSermon;
        _loadSermonForEdit(freshSermon);
      }
    } catch (e) {
      // Silently fail for background refresh
    }
  }

  Future<void> _updateSermon() async {
    if (_formKey.currentState!.validate()) {
      // Save the description content to the controller
      if (_description.trim().isNotEmpty) {
        controller.descriptionFormController.text = _description;
      }

      final success = await controller.updateSermon(widget.sermonId);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sermon updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.go('/sermons');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edit Sermon')),
        body: const Center(child: CircleLoadingAnimation()),
      );
    }

    if (sermon == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edit Sermon')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Failed to load sermon'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/sermons'),
                child: const Text('Back to Sermons'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Sermon'),
        actions: [
          Obx(
            () =>
                controller.isSubmitting.value
                    ? const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircleLoadingAnimation(strokeWidth: 2),
                        ),
                      ),
                    )
                    : TextButton.icon(
                      onPressed: _updateSermon,
                      icon: const Icon(IconlyLight.tickSquare),
                      label: const Text('Update'),
                    ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: SizeConfig.screenWidth * 0.013,
            vertical: SizeConfig.screenHeight * 0.1,
          ),
          child: Center(
            child: Card(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("Edit Sermon", style: theme.textTheme.titleLarge),
                      Gap(10.h),
                      Text(
                        "Title of the sermon",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      CustomTextFormField(
                        controller: controller.titleFormController,
                        hintText: 'Enter sermon title',
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a title';
                          }
                          return null;
                        },
                      ),
                      Gap(16.h),
                      Text(
                        "Category",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Gap(8.h),
                      CustomAutocomplete(
                        options: controller.categories,
                        controller: controller.categoryController,
                        hintText: 'Enter or select sermon category',
                        onSelected: (value) {},
                      ),
                      Gap(16.h),

                      // Description Field with Markup Editor
                      Obx(() {
                        // Check if we're editing and need to set initial content
                        if (controller.editingDescription.value.isNotEmpty) {
                          _description = controller.editingDescription.value;
                          // Clear the editing description after setting it
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            controller.editingDescription.value = '';
                          });
                        }

                        return MarkupEditorWidget(
                          label: "Description",
                          isRequired: true,
                          hintText:
                              "Enter sermon description with rich formatting",
                          height: 250.h,
                          initialValue: _description,
                          onChanged: (content) {
                            _description = content;
                          },
                        );
                      }),
                      Gap(16.h),
                      Card(
                        elevation: 0,
                        color: Colors.grey.shade50,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(IconlyLight.image),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Media Items',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              Gap(12.h),
                              MediaUploadWidget(
                                category: 'SERMON',
                                multipleSelect: true,
                                onMediaSelected: (selectedMedia) {
                                  controller.mediaTypeValue.value =
                                      selectedMedia[0].type ?? '';
                                  controller.mediaItems.addAll(selectedMedia);
                                },
                              ),
                              Gap(12.h),
                              Obx(
                                () =>
                                    controller.mediaItems.isEmpty
                                        ? Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(16.0),
                                            child: Text(
                                              'No media items added yet',
                                              style: TextStyle(
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ),
                                        )
                                        : ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount:
                                              controller.mediaItems.length,
                                          itemBuilder: (context, index) {
                                            final item =
                                                controller.mediaItems[index];
                                            return ListTile(
                                              contentPadding: EdgeInsets.zero,
                                              leading: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                                child: Image.network(
                                                  item.mediaUrl ?? '',
                                                  width: 40,
                                                  height: 40,
                                                  fit: BoxFit.cover,
                                                  errorBuilder:
                                                      (
                                                        context,
                                                        error,
                                                        stackTrace,
                                                      ) => Container(
                                                        width: 40,
                                                        height: 40,
                                                        color:
                                                            Colors
                                                                .grey
                                                                .shade300,
                                                        child: const Icon(
                                                          Icons.error,
                                                          size: 20,
                                                        ),
                                                      ),
                                                ),
                                              ),
                                              title: Text(
                                                item.title ?? 'Media Item',
                                              ),
                                              subtitle: Text(
                                                item.mediaUrl ?? '',
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              trailing: IconButton(
                                                icon: const Icon(
                                                  Icons.delete,
                                                  color: Colors.red,
                                                ),
                                                onPressed:
                                                    () => controller
                                                        .removeMediaItem(index),
                                                tooltip: 'Remove',
                                              ),
                                            );
                                          },
                                        ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Gap(16.h),
                      Obx(
                        () => Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: SizeConfig.screenWidth * 0.16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    context.go('/sermons');
                                  },
                                  icon: const Icon(IconlyLight.arrowLeft),
                                  label: const Text('Cancel'),
                                ),
                              ),
                              Gap(16.w),
                              Expanded(
                                child: CustomButton(
                                  onPressed:
                                      controller.isSubmitting.value
                                          ? null
                                          : _updateSermon,
                                  icon:
                                      controller.isSubmitting.value
                                          ? SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircleLoadingAnimation(
                                              strokeWidth: 2,
                                            ),
                                          )
                                          : const Icon(IconlyLight.tickSquare),
                                  label: const Text('Update'),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
