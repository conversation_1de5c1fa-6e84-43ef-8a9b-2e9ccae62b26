import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import '../../controllers/staff_controller.dart';
import '../../models/staff_model.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class ViewStaffScreen extends StatefulWidget {
  final String staffId;

  const ViewStaffScreen({super.key, required this.staffId});

  @override
  State<ViewStaffScreen> createState() => _ViewStaffScreenState();
}

class _ViewStaffScreenState extends State<ViewStaffScreen> {
  final StaffController controller = Get.find<StaffController>();
  final RxBool isLoading = true.obs;
  final Rx<StaffModel?> staff = Rx<StaffModel?>(null);

  @override
  void initState() {
    super.initState();
    _loadStaffData();
  }

  Future<void> _loadStaffData() async {
    isLoading.value = true;
    try {
      final staffData = await controller.getStaffById(widget.staffId);
      if (staffData != null) {
        staff.value = staffData;
      } else {
        ToastUtils.showErrorToast('Failed to load staff member', null);
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error loading staff details', null);
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Staff Details'),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.edit),
            onPressed:
                () => context.go('${Routes.STAFF}/${widget.staffId}/edit'),
            tooltip: 'Edit Staff',
          ),
        ],
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircleLoadingAnimation());
        }

        if (staff.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  IconlyLight.dangerCircle,
                  size: 48,
                  color: Colors.red,
                ),
                const Gap(16),
                const Text('Staff member not found'),
                const Gap(16),
                ElevatedButton(
                  onPressed: () => context.go(Routes.STAFF),
                  child: const Text('Back to Staff List'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileHeader(),
              const Divider(height: 32),
              _buildInfoSection('Personal Information'),
              _buildInfoItem(
                'Phone Number',
                staff.value?.phoneNumber ?? 'N/A',
                IconlyLight.call,
              ),
              _buildInfoItem(
                'Email',
                staff.value?.email ?? 'N/A',
                IconlyLight.message,
              ),
              _buildInfoItem(
                'ID Number',
                staff.value?.idNumber ?? 'N/A',
                IconlyLight.document,
              ),
              _buildInfoItem(
                'Staff Code',
                staff.value?.code ?? 'N/A',
                IconlyLight.scan,
              ),

              const Divider(height: 32),
              _buildInfoSection('Role & Permissions'),
              _buildInfoItem(
                'Role',
                staff.value?.roleAssignments?.isNotEmpty ?? false
                    ? staff.value!.roleAssignments![0].toString()
                    : 'No role assigned',
                IconlyLight.profile,
              ),
              _buildPermissionItem('Maker', staff.value?.isMaker ?? false),
              _buildPermissionItem('Checker', staff.value?.isChecker ?? false),
              _buildPermissionItem(
                'Signatory',
                staff.value?.isSignatory ?? false,
              ),

              const Divider(height: 32),
              _buildInfoSection('Status Information'),
              _buildStatusItem('Status', staff.value?.status ?? 'Unknown'),
              _buildInfoItem(
                'Created At',
                staff.value?.createdAt != null
                    ? _formatDate(staff.value!.createdAt!.toIso8601String())
                    : 'N/A',
                IconlyLight.calendar,
              ),

              const Gap(32),
              SizedBox(
                width: double.infinity,
                child: CustomButton(
                  onPressed:
                      () =>
                          context.go('${Routes.STAFF}/${widget.staffId}/edit'),
                  icon: const Icon(IconlyLight.edit),
                  label: const Text('Edit Staff Member'),
                ),
              ),
              const Gap(16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _confirmDelete(context),
                  icon: const Icon(IconlyLight.delete, color: Colors.red),
                  label: const Text(
                    'Delete Staff Member',
                    style: TextStyle(color: Colors.red),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildProfileHeader() {
    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(
              context,
            ).colorScheme.primary.withOpacity(0.2),
            child: Icon(
              IconlyBold.profile,
              size: 50,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const Gap(16),
          Text(
            '${staff.value?.firstName ?? ''} ${staff.value?.secondName ?? ''}',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const Gap(8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(staff.value?.status).withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              staff.value?.status ?? 'Unknown',
              style: TextStyle(
                color: _getStatusColor(staff.value?.status),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, size: 24, color: Theme.of(context).colorScheme.primary),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(color: Colors.grey, fontSize: 14),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(
            value ? IconlyBold.tickSquare : IconlyLight.closeSquare,
            size: 24,
            color: value ? Colors.green : Colors.grey,
          ),
          const Gap(16),
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: value ? Colors.black87 : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String status) {
    final color = _getStatusColor(status);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(IconlyLight.tickSquare, size: 24, color: color),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(color: Colors.grey, fontSize: 14),
                ),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.grey;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text(
              'Are you sure you want to delete this staff member?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              CustomButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteStaff();
                },
                
                label: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _deleteStaff() async {
    final result = await controller.deleteStaff(widget.staffId);
    if (result) {
      ToastUtils.showSuccessToast('Staff member deleted successfully', null);
      if (mounted) {
        context.go(Routes.STAFF);
      }
    }
  }
}
