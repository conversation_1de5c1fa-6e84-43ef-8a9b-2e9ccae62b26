import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/members/presentation/screens/members_screen/members_screen.dart';
import '../../../controllers/relationship_controller.dart';
import '../../../controllers/member_controller.dart';
import 'add_relationship_type_dialog.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class AddRelationshipContent extends StatefulWidget {
  final RelationshipController relationshipController;
  final String memberId;
  final VoidCallback? onClose;

  const AddRelationshipContent({
    super.key,
    required this.relationshipController,
    required this.memberId,
    this.onClose,
  });

  @override
  State<AddRelationshipContent> createState() => _AddRelationshipContentState();
}

class _AddRelationshipContentState extends State<AddRelationshipContent> {
  final formKey = GlobalKey<FormState>();
  late final MemberController memberController;

  // Map to store relationship type selections for each member
  final memberRelationships = <String, String>{}.obs;

  // Loading state
  final isAdding = false.obs;

  @override
  void initState() {
    super.initState();
    memberController = Get.find<MemberController>();

    // Clear any previously selected members
    memberController.selectedMembers.clear();

    // Load relationship types if not already loaded
    if (widget.relationshipController.relationshipTypes.isEmpty) {
      widget.relationshipController.fetchRelationshipTypes();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Form(
      key: formKey,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Create relationships between this member and other members',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            Gap(16.h),

            // Check if relationship types are available
            Obx(() {
              if (widget.relationshipController.relationshipTypes.isEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'No relationship types available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                    Gap(8.h),
                    ElevatedButton.icon(
                      onPressed: () {
                        if (widget.onClose != null) {
                          widget.onClose!();
                        }
                        showAddRelationshipTypeDialog(
                          context,
                          widget.relationshipController,
                        );
                      },
                      icon: const Icon(IconlyLight.plus),
                      label: const Text('Create Relationship Type'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ],
                );
              }

              return const SizedBox.shrink();
            }),

            Gap(16.h),

            // Member selection
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Related Members', style: theme.textTheme.titleSmall),
                Obx(
                  () => Text(
                    '${memberController.selectedMembers.length} selected',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            Gap(8.h),
            InkWell(
              onTap: () {
                // Navigate to members screen with isSelect=true
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MemberViewScreen(isSelect: true),
                  ),
                );
              },
              borderRadius: BorderRadius.circular(8.r),
              child: Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: colorScheme.outline.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: colorScheme.onSurface.withOpacity(0.1),
                      child: Icon(
                        IconlyLight.profile,
                        color: colorScheme.onSurface.withOpacity(0.5),
                        size: 20.r,
                      ),
                    ),
                    Gap(12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Select members',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                          Text(
                            'Click to select members',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      IconlyLight.arrowRight2,
                      color: colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ],
                ),
              ),
            ),

            // Selected members list
            Expanded(
              child: Obx(() {
                if (memberController.selectedMembers.isEmpty) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: Center(
                      child: Text(
                        'No members selected',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ),
                  );
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(16.h),
                    Text('Selected Members', style: theme.textTheme.titleSmall),
                    Gap(8.h),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: colorScheme.outline.withOpacity(0.2),
                          ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: ListView.separated(
                          shrinkWrap: true,
                          itemCount: memberController.selectedMembers.length,
                          separatorBuilder:
                              (context, index) => Divider(
                                height: 1,
                                color: colorScheme.outline.withOpacity(0.1),
                              ),
                          itemBuilder: (context, index) {
                            final member =
                                memberController.selectedMembers[index];
                            final memberId = member.id!;

                            return Column(
                              children: [
                                ListTile(
                                  leading: CircleAvatar(
                                    radius: 16.r,
                                    backgroundColor: colorScheme.primary
                                        .withOpacity(0.1),
                                    backgroundImage:
                                        member.profileUrl != null
                                            ? NetworkImage(member.profileUrl!)
                                            : null,
                                    child:
                                        member.profileUrl == null
                                            ? Icon(
                                              IconlyBold.profile,
                                              color: colorScheme.primary,
                                              size: 16.r,
                                            )
                                            : null,
                                  ),
                                  title: Text(
                                    '${member.firstName ?? ''} ${member.secondName ?? ''}',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  subtitle: Text(
                                    member.phoneNumber ?? 'No phone',
                                    style: theme.textTheme.bodySmall,
                                  ),
                                  trailing: IconButton(
                                    icon: Icon(
                                      IconlyLight.delete,
                                      color: colorScheme.error,
                                      size: 18.r,
                                    ),
                                    onPressed: () {
                                      memberController.selectedMembers.removeAt(
                                        index,
                                      );
                                      // Also remove from relationship map
                                      memberRelationships.remove(memberId);
                                    },
                                  ),
                                  dense: true,
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                    left: 12.w,
                                    right: 8.w,
                                    bottom: 8.h,
                                  ),
                                  child: DropdownButtonFormField<String>(
                                    value: memberRelationships[memberId],
                                    decoration: InputDecoration(
                                      labelText: 'Relationship Type',
                                      hintText: 'Select type',
                                      isDense: true,
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12.w,
                                        vertical: 8.h,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                          8.r,
                                        ),
                                      ),
                                    ),
                                    items:
                                        widget
                                            .relationshipController
                                            .relationshipTypes
                                            .map((type) {
                                              return DropdownMenuItem<String>(
                                                value: type.id,
                                                child: Text(
                                                  type.title ?? 'Unknown',
                                                ),
                                              );
                                            })
                                            .toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        memberRelationships[memberId] = value;
                                      }
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Required';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                );
              }),
            ),

            // Save button
            Gap(16.h),
            SizedBox(
              width: double.infinity,
              child: Obx(
                () => ElevatedButton.icon(
                  onPressed:
                      memberController.selectedMembers.isEmpty || isAdding.value
                          ? null
                          : () => _saveRelationships(),
                  icon: const Icon(IconlyLight.tickSquare),
                  label:
                      isAdding.value
                          ? SizedBox(
                            height: 20.h,
                            width: 20.h,
                            child: CircleLoadingAnimation(
                              color: colorScheme.onPrimary,
                              strokeWidth: 2,
                            ),
                          )
                          : Text(
                            'Save (${memberController.selectedMembers.length})',
                          ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h,
                    ),
                    disabledBackgroundColor: colorScheme.primary.withOpacity(
                      0.3,
                    ),
                    disabledForegroundColor: colorScheme.onPrimary.withOpacity(
                      0.5,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveRelationships() async {
    if (!formKey.currentState!.validate()) return;

    // Check if all members have relationship types assigned
    bool allMembersHaveRelationships = true;
    for (final member in memberController.selectedMembers) {
      if (!memberRelationships.containsKey(member.id) ||
          memberRelationships[member.id]!.isEmpty) {
        allMembersHaveRelationships = false;
        break;
      }
    }

    if (!allMembersHaveRelationships) {
      ToastUtils.showErrorToast(
        'Please assign relationship types to all selected members',
        null,
      );
      return;
    }

    isAdding(true);

    try {
      // Create a list of relationship objects
      final List<Map<String, dynamic>> relationshipsList = [];

      for (final member in memberController.selectedMembers) {
        final memberId = member.id!;
        final relationshipTypeId = memberRelationships[memberId]!;

        relationshipsList.add({
          'from_member_id':
              widget.relationshipController.selectedMemberId.value,
          'to_member_id': memberId,
          'relationship_type_id': relationshipTypeId,
        });
      }

      // Send all relationships at once
      final success = await widget.relationshipController
          .createMemberRelationships(relationships: relationshipsList);

      // Show result message
      if (success) {
        ToastUtils.showSuccessToast(
          'Successfully created ${relationshipsList.length} relationships',
          null,
        );

        // Clear selected members and close
        memberController.selectedMembers.clear();
        if (widget.onClose != null) {
          widget.onClose!();
        }
      } else {
        ToastUtils.showErrorToast('Failed to create relationships', null);
      }
    } finally {
      isAdding(false);
    }
  }
}
