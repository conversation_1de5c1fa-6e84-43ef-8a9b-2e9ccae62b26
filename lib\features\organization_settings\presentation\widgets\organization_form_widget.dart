import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import '../../controllers/organization_settings_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class OrganizationFormWidget extends StatelessWidget {
  const OrganizationFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrganizationSettingsController>();
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              children: [
                Icon(IconlyLight.edit, color: theme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Organization Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Organization Name
            TextFormField(
              controller: controller.nameController,
              decoration: InputDecoration(
                labelText: 'Organization Name',
                hintText: 'Enter organization name',
                prefixIcon: Icon(IconlyLight.home),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Organization name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Description
            TextFormField(
              controller: controller.descriptionController,
              decoration: InputDecoration(
                labelText: 'Description',
                hintText: 'Enter organization description',
                prefixIcon: Icon(IconlyLight.document),
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Description is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email
            TextFormField(
              controller: controller.emailController,
              decoration: InputDecoration(
                labelText: 'Email Address',
                hintText: 'Enter email address',
                prefixIcon: Icon(IconlyLight.message),
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Email address is required';
                }
                if (!GetUtils.isEmail(value.trim())) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Phone Number
            TextFormField(
              controller: controller.phoneController,
              decoration: InputDecoration(
                labelText: 'Phone Number',
                hintText: 'Enter phone number',
                prefixIcon: Icon(IconlyLight.call),
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Phone number is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Username
            TextFormField(
              controller: controller.usernameController,
              decoration: InputDecoration(
                labelText: 'Username',
                hintText: 'Enter username',
                prefixIcon: Icon(IconlyLight.profile),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Username is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Business Number
            TextFormField(
              controller: controller.businessNumberController,
              decoration: InputDecoration(
                labelText: 'Business Number',
                hintText: 'Enter business registration number',
                prefixIcon: Icon(IconlyLight.work),
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // KRA PIN
            TextFormField(
              controller: controller.kraPinController,
              decoration: InputDecoration(
                labelText: 'KRA PIN',
                hintText: 'Enter KRA PIN',
                prefixIcon: Icon(IconlyLight.lock),
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Slogan
            TextFormField(
              controller: controller.sloganController,
              decoration: InputDecoration(
                labelText: 'Slogan',
                hintText: 'Enter organization slogan',
                prefixIcon: Icon(IconlyLight.star),
                border: const OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 24),

            // Update Button
            Obx(
              () => SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed:
                      controller.isUpdating.value
                          ? null
                          : () => controller.updateOrganization(),
                  child:
                      controller.isUpdating.value
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircleLoadingAnimation(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : Text(
                            'Update Organization',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
