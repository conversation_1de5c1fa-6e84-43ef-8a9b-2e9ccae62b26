import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:onechurch/core/widgets/html/html_display_widget.dart';
// import '../html_display_widget.dart';
import 'markup_editor_widget.dart';

/// Example screen showing how to use HTML display widgets
class HtmlDisplayExampleScreen extends StatefulWidget {
  const HtmlDisplayExampleScreen({super.key});

  @override
  State<HtmlDisplayExampleScreen> createState() =>
      _HtmlDisplayExampleScreenState();
}

class _HtmlDisplayExampleScreenState extends State<HtmlDisplayExampleScreen> {
  String _htmlContent = '';
  final GlobalKey<State<MarkupEditorWidget>> _editorKey = GlobalKey();

  // Sample HTML content for demonstration
  final String _sampleHtml = '''
    <h1>Welcome to Our Church</h1>
    <p>This is a <b>bold</b> announcement with <i>italic</i> text and <u>underlined</u> content.</p>
    
    <h2>Upcoming Events</h2>
    <ul>
      <li>Sunday Service at 9:00 AM</li>
      <li>Bible Study on Wednesday at 7:00 PM</li>
      <li>Youth Group on Friday at 6:00 PM</li>
    </ul>
    
    <blockquote>
      "For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life." - John 3:16
    </blockquote>
    
    <p>Visit our website at <a href="https://example.com">example.com</a> for more information.</p>
    
    <h3>Code Example</h3>
    <pre><code>function greet() {
      console.log("Hello, World!");
    }</code></pre>
  ''';

  @override
  void initState() {
    super.initState();
    _htmlContent = _sampleHtml;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('HTML Display Examples'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Editor Section
            Text(
              'HTML Editor',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(8),
            MarkupEditorWidget(
              key: _editorKey,
              initialValue: _htmlContent,
              height: 200,
              label: 'Content Editor',
              hintText: 'Enter your content here...',
              onChanged: (content) {
                setState(() {
                  _htmlContent = content;
                });
              },
            ),
            const Gap(24),

            // Basic Display
            Text(
              'Basic HTML Display',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(8),
            HtmlDisplayWidget(
              htmlContent: _htmlContent,
              showBorder: true,
              padding: const EdgeInsets.all(16),
            ),
            const Gap(24),

            // Card Display
            Text(
              'Card Style Display',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(8),
            HtmlDisplayCard(
              htmlContent: _htmlContent,
              title: 'Church Announcement',
              actions: [
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: () {
                    // Handle share
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Share functionality')),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    // Handle edit
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Edit functionality')),
                    );
                  },
                ),
              ],
              onTap: () {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Card tapped')));
              },
            ),
            const Gap(24),

            // Preview Display
            Text(
              'Preview Display (for lists)',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: theme.dividerColor),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: HtmlDisplayPreview(
                htmlContent: _htmlContent,
                maxLines: 2,
                onShowMore: () {
                  _showFullContentDialog();
                },
              ),
            ),
            const Gap(24),

            // Multiple Display Examples
            Text(
              'Multiple Display Styles',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(8),

            // Compact style
            HtmlDisplayCard(
              htmlContent:
                  '<h3>Quick Update</h3><p>This is a <b>quick update</b> for all members.</p>',
              title: 'Compact Style',
              elevation: 1.0,
            ),

            // With custom background
            HtmlDisplayCard(
              htmlContent:
                  '<p>This card has a <i>custom background</i> color.</p>',
              title: 'Custom Background',
              backgroundColor: theme.primaryColor.withOpacity(0.1),
            ),

            // Empty content example
            HtmlDisplayCard(
              htmlContent: '',
              title: 'Empty Content Example',
              emptyMessage: 'No content has been added yet.',
            ),

            Gap(24.h),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _htmlContent = _sampleHtml;
                      });
                    },
                    child: const Text('Load Sample'),
                  ),
                ),
                Gap(16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _htmlContent = '';
                      });
                    },
                    child: const Text('Clear Content'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showFullContentDialog() {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: Column(
                children: [
                  AppBar(
                    title: const Text('Full Content'),
                    automaticallyImplyLeading: false,
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: HtmlDisplayWidget(htmlContent: _htmlContent),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
