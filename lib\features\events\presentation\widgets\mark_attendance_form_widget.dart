import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/core/widgets/custom_phone_input.dart';
import 'package:onechurch/core/app/services/http_service.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../../../data/models/event_model.dart';
import '../../../attendance/controllers/attendance_controller.dart';
import '../../../../core/app/services/location_service.dart';

class MarkAttendanceFormWidget extends StatefulWidget {
  final EventModel event;
  final VoidCallback onSuccess;

  const MarkAttendanceFormWidget({
    super.key,
    required this.event,
    required this.onSuccess,
  });

  @override
  State<MarkAttendanceFormWidget> createState() =>
      _MarkAttendanceFormWidgetState();
}

class _MarkAttendanceFormWidgetState extends State<MarkAttendanceFormWidget> {
  late AttendanceController _attendanceController;
  late LocationService _locationService;
  late HttpService _httpService;
  final _formKey = GlobalKey<FormState>();

  // Phone lookup variables
  final TextEditingController _phoneController = TextEditingController();
  final RxBool _isPhoneValid = false.obs;
  final RxBool _isPhoneChecking = false.obs;
  final RxBool _isMemberFound = false.obs;
  final RxMap<String, dynamic> _memberData = <String, dynamic>{}.obs;

  @override
  void initState() {
    super.initState();
    _attendanceController = Get.find<AttendanceController>();
    _locationService = Get.find<LocationService>();
    _httpService = Get.find<HttpService>();
    _prefillEventData();
    _initializeLocation();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  /// Initialize location service and get current location silently
  Future<void> _initializeLocation() async {
    try {
      // Silently request location permissions if not granted
      if (!_locationService.isPermissionGranted) {
        await _locationService.requestPermissions();
      }

      // Silently get current location and auto-fill coordinates
      if (_locationService.isPermissionGranted) {
        await _locationService.getCurrentLocation();
      }
    } catch (e) {
      // Silent failure - location will be handled gracefully without user notification
      debugPrint('Location initialization failed silently: $e');
    }
  }

  void _prefillEventData() {
    // Prefill event-related data
    _attendanceController.titleController.text = widget.event.title ?? '';
    // Prefill location name from event module (not LocationService)
    _attendanceController.locationNameController.text =
        widget.event.location ?? '';

    // Set description if available
    if (widget.event.description != null &&
        widget.event.description!.isNotEmpty) {
      _attendanceController.descriptionController.text =
          widget.event.description!;
      _attendanceController.description.value = widget.event.description!;
    }

    // Clear attendee fields for new entry
    _attendanceController.attendeeNameFormController.clear();
    _attendanceController.attendeeEmailController.clear();

    // Clear phone lookup fields
    _phoneController.clear();
    _isMemberFound.value = false;
    _memberData.clear();
  }

  /// Handle phone number validation
  void _onPhoneNumberChanged(PhoneNumber number) {
    _phoneController.text = number.phoneNumber ?? '';
    _isPhoneValid.value =
        number.phoneNumber != null &&
        number.phoneNumber!.isNotEmpty &&
        number.phoneNumber!.length > 8;
  }

  /// Check if phone number belongs to a member and prefill data
  Future<void> _checkMemberByPhone() async {
    if (!_isPhoneValid.value) return;

    _isPhoneChecking.value = true;
    _isMemberFound.value = false;
    _memberData.clear();

    try {
      final response = await _httpService.request(
        url:
            "${ApiUrls.getSingleMember}?identifier=${_phoneController.text.trim().replaceAll('+', '')}",
        method: Method.GET,
      );

      if (response.data["status"] ?? false) {
        final data = response.data["data"];
        if (data != null) {
          _isMemberFound.value = true;
          _memberData.value = data;

          // Pre-fill form fields with member data
          _attendanceController.attendeeNameFormController.text =
              '${data["first_name"] ?? ''} ${data["second_name"] ?? ''}'.trim();
          _attendanceController.attendeeEmailController.text =
              data["email"] ?? '';
        } else {
          _isMemberFound.value = false;
        }
      } else {
        _isMemberFound.value = false;
        // Don't show error - just allow manual entry
      }
    } catch (e) {
      _isMemberFound.value = false;
      debugPrint('Error checking member by phone: $e');
    } finally {
      _isPhoneChecking.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Info Section
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primaryContainer.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        IconlyLight.calendar,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                      Gap(8.w),
                      Text(
                        'Event Information',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  Gap(12.h),
                  Text(
                    'Event: ${widget.event.title ?? 'Untitled Event'}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (widget.event.location != null &&
                      widget.event.location!.isNotEmpty) ...[
                    Gap(4.h),
                    Text(
                      'Location: ${widget.event.location}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ),
            Gap(24.h),

            // Attendee Information Section
            Text(
              'Attendee Information',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Gap(8.h),
            Text(
              'Enter phone number to auto-fill member details',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            Gap(16.h),

            // Phone Number Lookup
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: CustomPhoneInput(
                    label: 'Phone Number',
                    controller: _phoneController,
                    onPhoneNumberChanged: _onPhoneNumberChanged,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter phone number';
                      }
                      return null;
                    },
                  ),
                ),
                Gap(8.w),
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 24.h,
                    ), // Align with phone input
                    child: Obx(
                      () => ElevatedButton(
                        onPressed:
                            _isPhoneValid.value && !_isPhoneChecking.value
                                ? _checkMemberByPhone
                                : null,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child:
                            _isPhoneChecking.value
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircleLoadingAnimation(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : const Icon(IconlyLight.search),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Gap(8.h),
            Obx(
              () =>
                  _isPhoneValid.value
                      ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Gap(16.h),

                          // Attendee Name field
                          CustomTextFormField(
                            controller:
                                _attendanceController
                                    .attendeeNameFormController,
                            labelText: 'Attendee Name *',
                            hintText:
                                'Enter full name or auto-filled from phone lookup',
                            prefixIcon: const Icon(IconlyLight.profile),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter attendee name';
                              }
                              return null;
                            },
                          ),
                          Gap(16.h),

                          // Attendee Email field
                          CustomTextFormField(
                            controller:
                                _attendanceController.attendeeEmailController,
                            labelText: 'Attendee Email',
                            hintText: 'Enter attendee email (optional)',
                            prefixIcon: const Icon(IconlyLight.message),
                            keyboardType: TextInputType.emailAddress,
                          ),
                          Gap(24.h),

                          // Additional Notes Section
                          Text(
                            'Additional Notes',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Gap(16.h),

                          MarkupEditorWidget(
                            label: "Notes",
                            isRequired: false,
                            hintText: 'Enter any additional notes (optional)',
                            height: 150.h,
                            initialValue:
                                _attendanceController
                                    .descriptionController
                                    .text,
                            onChanged: (content) {
                              _attendanceController.description.value = content;
                            },
                          ),
                          Gap(24.h),

                          // Location Override Section
                          Text(
                            'Location',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Gap(8.h),
                          Text(
                            'Override the event location if needed',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                          Gap(16.h),

                          // Location Name Override
                          CustomTextFormField(
                            controller:
                                _attendanceController.locationNameController,
                            labelText: 'Location Name',
                            hintText: 'Auto-detected or enter custom location',
                            prefixIcon: const Icon(IconlyLight.location),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter location name';
                              }
                              return null;
                            },
                          ),
                        ],
                      )
                      : const SizedBox.shrink(),
            ),
            Gap(32.h),

            // Submit button - only show when phone is valid
            Obx(
              () =>
                  _isPhoneValid.value
                      ? Container(
                        width: double.infinity,
                        padding: EdgeInsets.only(bottom: 32.h),
                        child: Obx(
                          () =>
                              _attendanceController.isLoading.value
                                  ? const Center(
                                    child: CircleLoadingAnimation(),
                                  )
                                  : CustomButton(
                                    onPressed: _submitForm,
                                    icon: const Icon(IconlyBold.tickSquare),
                                    label: const Text('Mark Attendance'),
                                   
                                  ),
                        ),
                      )
                      : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (!_locationService.hasValidLocation) {
        try {
          await _locationService.getCurrentLocation();
        } catch (e) {
          // Silent failure - proceed without location
          debugPrint('Location not available for attendance: $e');
        }
      }

      final success = await _attendanceController.markAttendance();
      if (success) {
        widget.onSuccess();
      }
    }
  }
}
