import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../controllers/staff_roles_controller.dart';
import '../../models/staff_role_model.dart';
import '../widgets/permission_selection_widget.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class CreateStaffRoleScreen extends StatefulWidget {
  final StaffRoleModel? role;

  const CreateStaffRoleScreen({super.key, this.role});

  @override
  State<CreateStaffRoleScreen> createState() => _CreateStaffRoleScreenState();
}

class _CreateStaffRoleScreenState extends State<CreateStaffRoleScreen> {
  final StaffRolesController controller = Get.find<StaffRolesController>();
  final _formKey = GlobalKey<FormState>();
  bool isEditMode = false;

  @override
  void initState() {
    super.initState();
    isEditMode = widget.role != null;

    if (isEditMode) {
      controller.setSelectedRole(widget.role!);
    } else {
      controller.resetForm();
    }

    // Fetch permissions if not already loaded
    if (controller.permissions.isEmpty) {
      controller.fetchPermissions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditMode ? 'Edit Staff Role' : 'Create Staff Role'),
      ),
      persistentFooterButtons: [
        OutlinedButton(
          onPressed: () {
            context.pop();
          },
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (_formKey.currentState!.validate()) {
              bool success;
              if (isEditMode) {
                success = await controller.updateRole();
              } else {
                success = await controller.createRole();
              }

              if (success) {
                context.pop();
                ToastUtils.showSuccessToast(
                  'Success',
                  controller.successMessage.value,
                );
              } else {
                ToastUtils.showErrorToast(
                  'Error',
                  controller.errorMessage.value,
                );
              }
            }
          },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: Obx(
            () =>
                controller.isCreating.value || controller.isUpdating.value
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircleLoadingAnimation(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                    : Text(isEditMode ? 'Update Role' : 'Create Role'),
          ),
        ),
      ],
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircleLoadingAnimation())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Role Information',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                CustomTextFormField(
                                  controller: controller.nameController,
                                  labelText: 'Role Name',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter a role name';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                                CustomTextFormField(
                                  controller: controller.descriptionController,
                                  labelText: 'Description',
                                  maxLines: 3,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter a description';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Permissions',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'Select the permissions for this role:',
                                  style: TextStyle(fontSize: 14),
                                ),
                                const SizedBox(height: 16),
                                PermissionSelectionWidget(
                                  permissions: controller.permissions,
                                  selectedPermissions:
                                      controller.selectedPermissions,
                                  onToggle:
                                      controller.togglePermissionSelection,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }
}
