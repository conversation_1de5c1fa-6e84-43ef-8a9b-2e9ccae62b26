import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../controller/media_controller.dart';
import '../../models/media_model.dart';
import 'media_confirmation_screen.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class MediaGalleryScreen extends StatefulWidget {
  final bool multipleSelect;
  final Function(List<MediaModel>) onMediaSelected;

  const MediaGalleryScreen({
    super.key,
    required this.multipleSelect,
    required this.onMediaSelected,
  });

  @override
  State<MediaGalleryScreen> createState() => _MediaGalleryScreenState();
}

class _MediaGalleryScreenState extends State<MediaGalleryScreen> {
  final MediaController _mediaController = Get.find<MediaController>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _mediaController.fetchMedia(refresh: true);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _mediaController.loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Media Gallery'),
        actions: [
          Obx(() {
            final hasSelection = _mediaController.selectedMedia.isNotEmpty;
            return IconButton(
              icon: Icon(
                Icons.check,
                color: hasSelection ? Colors.green : Colors.grey,
              ),
              onPressed:
                  hasSelection ? () => _navigateToConfirmation(context) : null,
            );
          }),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(),
          Expanded(
            child: Obx(() {
              if (_mediaController.isLoading.value &&
                  _mediaController.mediaItems.isEmpty) {
                return const Center(child: CircleLoadingAnimation());
              }

              if (_mediaController.mediaItems.isEmpty) {
                return const Center(child: Text('No media items found'));
              }

              return GridView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1,
                ),
                itemCount:
                    _mediaController.mediaItems.length +
                    (_mediaController.hasMoreData.value ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index >= _mediaController.mediaItems.length) {
                    return const Center(child: CircleLoadingAnimation());
                  }

                  final media = _mediaController.mediaItems[index];
                  final isSelected = _mediaController.isMediaSelected(media);

                  return GestureDetector(
                    onTap: () {
                      _mediaController.toggleMediaSelection(
                        media,
                        widget.multipleSelect,
                      );
                      if (!widget.multipleSelect) {
                        _navigateToConfirmation(context);
                      }
                    },
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color:
                                  isSelected
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey[300]!,
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(3),
                            child:
                                media.mediaUrl != null
                                    ? Image.network(
                                      media.mediaUrl!,
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              const Center(
                                                child: Icon(
                                                  Icons.broken_image,
                                                  size: 40,
                                                ),
                                              ),
                                      loadingBuilder: (
                                        context,
                                        child,
                                        loadingProgress,
                                      ) {
                                        if (loadingProgress == null) {
                                          return child;
                                        }
                                        return Center(
                                          child: CircleLoadingAnimation(
                                            value:
                                                loadingProgress
                                                            .expectedTotalBytes !=
                                                        null
                                                    ? loadingProgress
                                                            .cumulativeBytesLoaded /
                                                        loadingProgress
                                                            .expectedTotalBytes!
                                                    : null,
                                          ),
                                        );
                                      },
                                    )
                                    : const Center(
                                      child: Icon(
                                        Icons.image_not_supported,
                                        size: 40,
                                      ),
                                    ),
                          ),
                        ),
                        if (isSelected)
                          Positioned(
                            top: 4,
                            right: 4,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Obx(
      () => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: 80,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _mediaController.titleController,
                hintText: 'Search by title',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => _mediaController.applyFilters(),
              child: const Text('Search'),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () => _mediaController.resetFilters(),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToConfirmation(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MediaConfirmationScreen(
              selectedMedia: _mediaController.selectedMedia.toList(),
              onConfirm: (media) {
                widget.onMediaSelected(media);
                Navigator.pop(context); // Close confirmation screen
                Navigator.pop(context); // Close gallery screen
              },
            ),
      ),
    );
  }
}
