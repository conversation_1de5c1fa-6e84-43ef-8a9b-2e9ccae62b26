import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  final String? text;
  final VoidCallback? onPressed;
  final Function()? onClick;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? textColor;
  final Widget? icon;
  final double? width;
  final double height;
  final Widget? label;
  final Color? color;

  const CustomButton({
    super.key,
    this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.onClick,
    this.width,
    this.height = 50,
    this.label, this.color,
  });

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      style: FilledButton.styleFrom(
        backgroundColor:
            backgroundColor ?? Theme.of(context).colorScheme.primary,
        foregroundColor: textColor ?? Theme.of(context).colorScheme.onPrimary,
        minimumSize: Size(width ?? double.infinity, height),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        disabledBackgroundColor: (backgroundColor ??
                Theme.of(context).colorScheme.primary)
            .withOpacity(0.6),
        disabledForegroundColor: (textColor ??
                Theme.of(context).colorScheme.onPrimary)
            .withOpacity(0.6),
      ),
      onPressed: () {
        onPressed ?? onClick;
      },
      child:
          isLoading
              ? const CircularProgressIndicator()
              : icon != null
              ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [icon!, const SizedBox(width: 8), Text(text ?? '')],
              )
              : Text(text ?? ''),
    );
  }
}
