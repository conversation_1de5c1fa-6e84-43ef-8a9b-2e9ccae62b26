import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../../../../core/app/utils/screen_breakpoints.dart';
import '../../../../../core/app/constants/enums.dart';
import '../../../controllers/relationship_controller.dart';

/// Show add relationship type dialog
void showAddRelationshipTypeDialog(
  BuildContext context, 
  RelationshipController relationshipController
) {
  final formKey = GlobalKey<FormState>();
  String selectedCategory = MemberRelationshipCategory.family.displayName;
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;
  final mediaQuery = MediaQuery.of(context);
  final isMobile = mediaQuery.size.width < ScreenBreakpoints.mobile;

  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Row(
          children: [
            Icon(IconlyLight.category, color: colorScheme.primary, size: 24),
            Gap(12.w),
            const Text('Add Relationship Type'),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Container(
          width: isMobile ? mediaQuery.size.width * 0.9 : 400.w,
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Create a new relationship type to define connections between members',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  Gap(16.h),
                  CustomTextFormField(
                    controller: relationshipController.typeTitleController,
                    labelText: 'Title',
                    hintText: 'Enter title (e.g., Parent, Spouse)',
                    prefixIcon: const Icon(IconlyLight.paper),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a title';
                      }
                      return null;
                    },
                  ),
                  Gap(16.h),
                  CustomTextFormField(
                    controller:
                        relationshipController.typeDescriptionController,
                    labelText: 'Description',
                    hintText: 'Enter a detailed description',
                    prefixIcon: const Icon(IconlyLight.document),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  Gap(16.h),
                  DropdownButtonFormField<String>(
                    value: selectedCategory,
                    decoration: InputDecoration(
                      labelText: 'Category',
                      prefixIcon: const Icon(IconlyLight.category),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    items:
                        MemberRelationshipCategory.values.map((category) {
                          return DropdownMenuItem<String>(
                            value: category.displayName,
                            child: Text(category.displayName),
                          );
                        }).toList(),
                    onChanged: (value) {
                      selectedCategory =
                          value ??
                          MemberRelationshipCategory.family.displayName;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  ),
                  Gap(16.h),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: colorScheme.onSurface.withOpacity(0.8)),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                final success = await relationshipController
                    .createRelationshipType(
                      title: relationshipController.typeTitleController.text,
                      description:
                          relationshipController
                              .typeDescriptionController
                              .text,
                      category: selectedCategory,
                      isGeneral: false,
                    );

                if (success) {
                  relationshipController.typeTitleController.clear();
                  relationshipController.typeDescriptionController.clear();
                  Navigator.pop(context);
                }
              }
            },
            icon: const Icon(IconlyLight.tickSquare),
            label: const Text('Save'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
          ),
        ],
      );
    },
  );
}
