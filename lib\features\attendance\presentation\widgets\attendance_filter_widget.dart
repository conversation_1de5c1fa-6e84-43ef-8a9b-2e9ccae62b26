import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../controllers/attendance_controller.dart';

class AttendanceFilterWidget extends StatelessWidget {
  const AttendanceFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AttendanceController>();

    return Card(
      margin: const EdgeInsets.all(12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(IconlyLight.filter, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Filters',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => controller.clearFilters(),
                  icon: const Icon(Icons.clear_all, size: 18),
                  label: const Text('Clear All'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                    visualDensity: VisualDensity.compact,
                  ),
                ),
              ],
            ),
            const Divider(),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: [
                // Search filter
                SizedBox(
                  width: 200.w,
                  child: CustomTextField(
                    controller: controller.searchController,
                    labelText: 'Search',
                    hintText: 'Search by identifier...',
                    prefixIcon: const Icon(IconlyLight.search, size: 18),

                    onChanged: (value) => controller.searchFilter.value = value,
                  ),
                ),

                // Location filter
                SizedBox(
                  width: 200.w,
                  child: CustomTextField(
                    controller: controller.locationController,
                    labelText: 'Location',
                    hintText: 'Filter by location...',
                    prefixIcon: const Icon(IconlyLight.location, size: 18),

                    onChanged:
                        (value) => controller.locationFilter.value = value,
                  ),
                ),

                // Date range filters
                SizedBox(
                  width: 200.w,
                  child: Obx(() {
                    final startDateText =
                        controller.startDateFilter.value != null
                            ? DateFormat(
                              'dd/MM/yyyy',
                            ).format(controller.startDateFilter.value!)
                            : 'Start Date';

                    return OutlinedButton.icon(
                      onPressed: () => _selectStartDate(context, controller),
                      icon: const Icon(IconlyLight.calendar, size: 18),
                      label: Text(startDateText),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.black87,
                        alignment: Alignment.centerLeft,
                        minimumSize: Size(200.w, 48),
                      ),
                    );
                  }),
                ),

                SizedBox(
                  width: 200.w,
                  child: Obx(() {
                    final endDateText =
                        controller.endDateFilter.value != null
                            ? DateFormat(
                              'dd/MM/yyyy',
                            ).format(controller.endDateFilter.value!)
                            : 'End Date';

                    return OutlinedButton.icon(
                      onPressed: () => _selectEndDate(context, controller),
                      icon: const Icon(IconlyLight.calendar, size: 18),
                      label: Text(endDateText),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.black87,
                        alignment: Alignment.centerLeft,
                        minimumSize: Size(200.w, 48),
                      ),
                    );
                  }),
                ),
              ],
            ),
            Gap(16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomButton(
                  onPressed: () => controller.applyFilters(),
                  icon: const Icon(IconlyLight.filter, size: 18),
                  label: const Text('Apply Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Select start date
  Future<void> _selectStartDate(
    BuildContext context,
    AttendanceController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDateFilter.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to start of day for inclusive filtering
      final startOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        0,
        0,
        0,
      );
      controller.startDateFilter.value = startOfDay;
    }
  }

  // Select end date
  Future<void> _selectEndDate(
    BuildContext context,
    AttendanceController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDateFilter.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.endDateFilter.value = endOfDay;
    }
  }
}
