import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import '../../controllers/organization_settings_controller.dart';
import '../widgets/organization_info_card.dart';
import '../widgets/organization_form_widget.dart';
import '../widgets/organization_acc_widget.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class OrganizationSettingsScreen extends StatelessWidget {
  const OrganizationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrganizationSettingsController>();
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Organization Settings',
          style: theme.appBarTheme.titleTextStyle,
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: theme.appBarTheme.elevation,
        iconTheme: theme.appBarTheme.iconTheme,
        actions: [
          IconButton(
            onPressed: () => controller.refreshOrganization(),
            icon: Icon(IconlyLight.arrowDown2),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CircleLoadingAnimation(
              valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
            ),
          );
        }

        if (controller.organization.value == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyLight.infoSquare,
                  size: 64,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(height: 16),
                Text(
                  'No organization data found',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.fetchOrganization(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => controller.refreshOrganization(),
          color: theme.primaryColor,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Organization Info Card
                const OrganizationInfoCard(),

                const SizedBox(height: 16),

                // Organization Form
                const OrganizationFormWidget(),

                const SizedBox(height: 16),

                // Organization Accounts
                const OrganizationAccountsWidget(),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      }),
    );
  }
}
