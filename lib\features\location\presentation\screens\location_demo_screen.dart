import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import '../../../../core/app/services/location_service.dart';
import '../../../../core/app/constants/enums.dart' as enums;
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
/// Demo screen showing how to use the LocationService
class LocationDemoScreen extends StatelessWidget {
  const LocationDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the LocationService instance
    final locationService = Get.find<LocationService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Service Demo'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            _buildStatusCard(locationService),
            const SizedBox(height: 16),

            // Current Location Card
            _buildLocationCard(locationService),
            const SizedBox(height: 16),

            // Actions Card
            _buildActionsCard(locationService),
            const SizedBox(height: 16),

            // Settings Card
            _buildSettingsCard(locationService),
            const SizedBox(height: 16),

            // Distance Demo Card
            _buildDistanceDemoCard(locationService),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(LocationService locationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.location, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Location Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusRow(
                    'Permission',
                    locationService.permissionStatus.displayName,
                    locationService.isPermissionGranted
                        ? Colors.green
                        : Colors.red,
                  ),
                  _buildStatusRow(
                    'Service',
                    locationService.serviceStatus.displayName,
                    locationService.isServiceEnabled
                        ? Colors.green
                        : Colors.red,
                  ),
                  _buildStatusRow(
                    'Tracking',
                    locationService.trackingStatus.displayName,
                    locationService.isTracking ? Colors.green : Colors.orange,
                  ),
                  if (locationService.isLoading)
                    const Row(
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircleLoadingAnimation(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Loading...'),
                      ],
                    ),
                  if (locationService.errorMessage.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              locationService.errorMessage,
                              style: TextStyle(
                                color: Colors.red.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard(LocationService locationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.location, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Current Location',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    'Coordinates',
                    locationService.formattedCoordinates,
                  ),
                  _buildInfoRow(
                    'Location Name',
                    locationService.locationName.isEmpty
                        ? 'Not available'
                        : locationService.locationName,
                  ),
                  _buildInfoRow(
                    'Has Valid Location',
                    locationService.hasValidLocation.toString(),
                  ),
                  _buildInfoRow(
                    'Accuracy',
                    locationService.accuracy.displayName,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(color: Colors.grey)),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard(LocationService locationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.setting, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  'Actions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionButton(
                  'Request Permissions',
                  Icons.security,
                  () async {
                    await locationService.requestPermissions();
                  },
                ),
                _buildActionButton(
                  'Start Tracking',
                  Icons.play_arrow,
                  () async {
                    await locationService.startTracking();
                  },
                ),
                _buildActionButton('Stop Tracking', Icons.stop, () async {
                  await locationService.stopTracking();
                }),
                _buildActionButton(
                  'Get Current Location',
                  Icons.my_location,
                  () async {
                    await locationService.getCurrentLocation();
                  },
                ),
                _buildActionButton(
                  'Open Location Settings',
                  Icons.settings_applications,
                  () async {
                    await locationService.openLocationSettings();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildSettingsCard(LocationService locationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.setting, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'Settings',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    'Accuracy',
                    locationService.accuracy.displayName,
                  ),
                  _buildInfoRow(
                    'Update Interval',
                    '${locationService.updateInterval}s',
                  ),
                  _buildInfoRow(
                    'Distance Filter',
                    '${locationService.distanceFilter}m',
                  ),
                  _buildInfoRow(
                    'Background Tracking',
                    locationService.enableBackgroundTracking.toString(),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () async {
                await locationService.updateSettings(
                  accuracy: enums.LocationAccuracy.best,
                  updateInterval: 5,
                  distanceFilter: 5,
                  enableBackgroundTracking: true,
                );
              },
              child: const Text('Update to Best Settings'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistanceDemoCard(LocationService locationService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyBold.discovery, color: Colors.teal),
                const SizedBox(width: 8),
                const Text(
                  'Distance Demo',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Example: Distance to San Francisco (37.7749, -122.4194)',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Obx(() {
              // Example coordinates: San Francisco
              const targetLat = 37.7749;
              const targetLng = -122.4194;

              final distance = locationService.getDistanceTo(
                targetLat,
                targetLng,
              );
              final bearing = locationService.getBearingTo(
                targetLat,
                targetLng,
              );
              final isWithin1km = locationService.isWithinRadius(
                targetLat,
                targetLng,
                1000,
              );

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    'Distance',
                    '${distance.toStringAsFixed(2)} meters',
                  ),
                  _buildInfoRow('Bearing', '${bearing.toStringAsFixed(2)}°'),
                  _buildInfoRow('Within 1km', isWithin1km.toString()),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
