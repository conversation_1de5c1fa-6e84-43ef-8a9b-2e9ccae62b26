import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../controllers/relationship_controller.dart';
import '../../../models/relationship_models.dart';

/// Confirm delete relationship type
void confirmDeleteRelationshipType(
  BuildContext context,
  RelationshipController relationshipController,
  String id,
) {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;

  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Row(
          children: [
            Icon(IconlyLight.delete, color: colorScheme.error, size: 24),
            Gap(12.w),
            const Text('Delete Relationship Type'),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this relationship type?',
              style: theme.textTheme.bodyMedium,
            ),
            Gap(16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: colorScheme.error.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    IconlyLight.infoSquare,
                    color: colorScheme.error,
                    size: 20,
                  ),
                  Gap(12.w),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. All relationships using this type will also be deleted.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: colorScheme.onSurface.withOpacity(0.8)),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.pop(context);
              await relationshipController.deleteRelationshipType(id);
            },
            icon: const Icon(IconlyLight.delete),
            label: const Text('Delete'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.error,
              foregroundColor: colorScheme.onError,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
          ),
        ],
      );
    },
  );
}

/// Confirm delete relationship
void confirmDeleteRelationship(
  BuildContext context,
  RelationshipController relationshipController,
  MemberRelationship relationship,
  String memberId,
) {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;

  // Get relationship details
  final relationshipType = relationship.relationshipType?.title ?? 'Unknown';
  final isFromMember = relationship.fromMemberId == memberId;

  // Use helper methods to get member name
  String memberName =
      isFromMember
          ? relationship.getToMemberName()
          : relationship.getFromMemberName();

  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Row(
          children: [
            Icon(IconlyLight.delete, color: colorScheme.error, size: 24),
            Gap(12.w),
            const Text('Delete Relationship'),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this relationship?',
              style: theme.textTheme.bodyMedium,
            ),
            Gap(16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Relationship Type:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Gap(8.w),
                      Text(relationshipType, style: theme.textTheme.bodyMedium),
                    ],
                  ),
                  Gap(8.h),
                  Row(
                    children: [
                      Text(
                        'Related Member:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Gap(8.w),
                      Text(memberName, style: theme.textTheme.bodyMedium),
                    ],
                  ),
                  Gap(8.h),
                  Row(
                    children: [
                      Text(
                        'Direction:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Gap(8.w),
                      Text(
                        isFromMember ? 'Outgoing' : 'Incoming',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                  if (relationship.description != null &&
                      relationship.description!.isNotEmpty) ...[
                    Gap(8.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description:',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Gap(8.w),
                        Expanded(
                          child: Text(
                            relationship.description!,
                            style: theme.textTheme.bodyMedium,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            Gap(16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: colorScheme.error.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    IconlyLight.infoSquare,
                    color: colorScheme.error,
                    size: 20,
                  ),
                  Gap(12.w),
                  Expanded(
                    child: Text(
                      'This action cannot be undone.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: colorScheme.onSurface.withOpacity(0.8)),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.pop(context);
              await relationshipController.deleteRelationship(relationship.id!);
            },
            icon: const Icon(IconlyLight.delete),
            label: const Text('Delete'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.error,
              foregroundColor: colorScheme.onError,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
          ),
        ],
      );
    },
  );
}
